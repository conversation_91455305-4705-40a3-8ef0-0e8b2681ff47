package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.ProcedureNameSegment;
import com.dc.parser.model.segment.ddl.routine.RoutineBodySegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Create procedure statement.
 */
@Getter
@Setter
public abstract class CreateProcedureStatement extends AbstractSQLStatement implements DDLStatement {

    private ProcedureNameSegment procedureName;
    
    /**
     * Get procedure name segment.
     *
     * @return procedure name segment
     */
    public Optional<ProcedureNameSegment> getProcedureName() {
        return Optional.ofNullable(procedureName);
    }

    /**
     * Get routine body.
     *
     * @return routine body
     */
    public Optional<RoutineBodySegment> getRoutineBody() {
        return Optional.empty();
    }
}
