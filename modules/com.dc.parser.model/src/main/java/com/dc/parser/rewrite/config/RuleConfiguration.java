package com.dc.parser.rewrite.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 自定义重写规则配置容器
 * <p>
 * 此类持有自定义重写规则的配置信息
 */
@RequiredArgsConstructor
@Getter
public final class RuleConfiguration {

    /**
     * 是否启用Oracle SELECT * 展开重写
     */
    private final boolean enableOracleSelectStarRewrite;

    /**
     * 是否启用DML影响行数预估SQL生成
     */
    private final boolean enableDMLPrecomputation;

    /**
     * Oracle SELECT * 重写的优先级
     */
    private final int oracleSelectStarPriority;

    /**
     * 创建默认配置，启用所有重写规则
     */
    public static RuleConfiguration defaultConfiguration() {
        return new RuleConfiguration(true, true, 100);
    }

    /**
     * 创建仅启用Oracle SELECT * 重写的配置
     */
    public static RuleConfiguration oracleSelectStarOnly() {
        return new RuleConfiguration(true, false, 100);
    }

    /**
     * 创建仅启用DML预估的配置
     */
    public static RuleConfiguration dmlPrecomputationOnly() {
        return new RuleConfiguration(false, true, 0);
    }
}
