package com.dc.parser.rewrite.common.generator.oracle;

import com.dc.infra.database.type.DatabaseType;
import com.dc.parser.model.context.SQLStatementContext;
import com.dc.parser.model.context.segment.select.projection.Projection;
import com.dc.parser.model.context.segment.select.projection.impl.ColumnProjection;
import com.dc.parser.model.context.statement.dml.SelectStatementContext;
import com.dc.parser.model.segment.dml.item.ProjectionSegment;
import com.dc.parser.model.segment.dml.item.ShorthandProjectionSegment;
import com.dc.parser.rewrite.common.generator.OptionalSQLTokenGenerator;
import com.dc.parser.rewrite.common.pojo.SQLToken;
import com.dc.parser.rewrite.common.pojo.generic.SubstitutableColumnNameToken;

import java.util.Collection;
import java.util.Optional;

/**
 * Oracle SELECT * Token生成器
 * 用于将Oracle数据库的 "SELECT *" 重写为具体的列名加ROWID。
 * <p>
 * 核心设计原则：
 * 1. 基于AST/SQLStatementContext
 * 2. 通过generateSQLToken方法返回SubstitutableColumnNameToken
 */
public final class OracleSelectStarTokenGenerator implements OptionalSQLTokenGenerator<SelectStatementContext> {

    @Override
    public boolean isGenerateSQLToken(final SQLStatementContext sqlStatementContext) {
        // 检查是否为Oracle数据库的SELECT语句
        if (!(sqlStatementContext instanceof SelectStatementContext
                && DatabaseType.Constant.ORACLE == sqlStatementContext.getDatabaseType().getType())) {
            return false;
        }

        SelectStatementContext selectStatementContext = (SelectStatementContext) sqlStatementContext;

        // 检查是否为无限定符的 * 投影，或者
        return selectStatementContext.getProjectionsContext().isUnqualifiedShorthandProjection()
                && selectStatementContext.getTablesContext().getSimpleTables().size() == 1;
    }

    @Override
    public SQLToken generateSQLToken(final SelectStatementContext selectStatementContext) {
        // 查找无限定符的 * 投影段
        Optional<ShorthandProjectionSegment> shorthandProjection = findUnqualifiedShorthandProjectionSegment(selectStatementContext);
        if (shorthandProjection.isEmpty()) {
            return null;
        }

        Collection<Projection> projections = selectStatementContext.getProjectionsContext().getExpandProjections();

        ColumnProjection rowIdProjection = new ColumnProjection(null, "ROWID", "__WHD_DC_JDBC_interal_ROWID__", selectStatementContext.getDatabaseType());
        projections.add(rowIdProjection);

        return new SubstitutableColumnNameToken(
                shorthandProjection.get().getStartIndex(),
                shorthandProjection.get().getStopIndex(),
                projections,
                selectStatementContext.getDatabaseType()
        );
    }

    /**
     * 查找无限定符的 * 投影段
     */
    private Optional<ShorthandProjectionSegment> findUnqualifiedShorthandProjectionSegment(final SelectStatementContext selectStatementContext) {
        for (ProjectionSegment each : selectStatementContext.getSqlStatement().getProjections().getProjections()) {
            if (each instanceof ShorthandProjectionSegment) {
                ShorthandProjectionSegment shorthand = (ShorthandProjectionSegment) each;
                if (shorthand.getOwner().isEmpty()) {
                    return Optional.of(shorthand);
                }
            }
        }
        return Optional.empty();
    }
}
