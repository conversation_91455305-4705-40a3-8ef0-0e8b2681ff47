
package com.dc.parser.model.statement.ddl;

import lombok.Getter;
import lombok.Setter;
import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

/**
 * Alter tablespace statement.
 */
@Getter
@Setter
public abstract class AlterTablespaceStatement extends AbstractSQLStatement implements DDLStatement {
    
    private TablespaceSegment tablespaceSegment;
    
    private TablespaceSegment renameTablespaceSegment;
    
    /**
     * Get rename tablespace.
     *
     * @return rename tablespace
     */
    public Optional<TablespaceSegment> getRenameTablespace() {
        return Optional.ofNullable(renameTablespaceSegment);
    }
}
