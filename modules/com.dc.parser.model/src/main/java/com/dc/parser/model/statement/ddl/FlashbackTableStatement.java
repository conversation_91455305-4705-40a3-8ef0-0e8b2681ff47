package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import lombok.Getter;
import lombok.Setter;

/**
 * Flashback table statement.
 */
@Getter
@Setter
public abstract class FlashbackTableStatement extends AbstractSQLStatement implements DDLStatement {

    private SimpleTableSegment table;

    private SimpleTableSegment renameTable;
}
