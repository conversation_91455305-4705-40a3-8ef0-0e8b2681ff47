package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

/**
 * Drop user mapping statement.
 */
public abstract class DropUserMappingStatement extends AbstractSQLStatement implements DDLStatement {


    public IdentifierValue getAuthorizationName() {
        return null;
    }

    public IdentifierValue getServerName() {
        return null;
    }
}
