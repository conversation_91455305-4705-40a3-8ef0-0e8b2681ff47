package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.FunctionNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;

import java.util.Optional;

public abstract class RefreshFunctionStatement extends AbstractSQLStatement implements DDLStatement {

    public Optional<FunctionNameSegment> getFunctionName() {
        return Optional.empty();
    }

    public void setFunctionName(FunctionNameSegment functionName) {
    }

}
