package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.routine.ProcedureNameSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;
import lombok.Getter;
import lombok.Setter;

import java.util.Optional;

/**
 * Drop procedure statement.
 */
@Getter
@Setter
public abstract class DropProcedureStatement extends AbstractSQLStatement implements DDLStatement {

    private ProcedureNameSegment procedureNameSegment;

    /**
     * Get procedure name segment.
     *
     * @return procedure name segment
     */
    public Optional<ProcedureNameSegment> getProcedureName() {
        return Optional.ofNullable(procedureNameSegment);
    }

    public IdentifierValue getSpecificName() {
        return null;
    }
}
