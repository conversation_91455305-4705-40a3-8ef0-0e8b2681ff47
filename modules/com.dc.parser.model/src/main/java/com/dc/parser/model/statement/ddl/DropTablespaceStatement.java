
package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.ddl.tablespace.TablespaceSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.collection.CollectionValue;

/**
 * Drop tablespace statement.
 */
public abstract class DropTablespaceStatement extends AbstractSQLStatement implements DDLStatement {

    public CollectionValue<TablespaceSegment> getTablespaces() {
        return null;
    }
}
