package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.dml.expr.ExpressionSegment;
import com.dc.parser.model.segment.generic.DataTypeSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

import java.util.Optional;

public abstract class DeclareVariableStatement extends AbstractSQLStatement implements DDLStatement {

    public boolean isOrReplace() {
        return false;
    }

    public void setOrReplace(boolean orReplace) {
    }

    public Optional<String> getVariableKeyword() {
        return Optional.empty();
    }

    public void setVariableKeyword(String variableKeyword) {
    }

    public Optional<IdentifierValue> getVariableName() {
        return Optional.empty();
    }

    public void setVariableName(IdentifierValue variableName) {
    }

    public Optional<DataTypeSegment> getDataTypeSegment() {
        return Optional.empty();
    }

    public void setDataTypeSegment(DataTypeSegment dataTypeSegment) {
    }

    public Optional<ExpressionSegment> getValue() {
        return Optional.empty();
    }

    public void setValue(ExpressionSegment value) {
    }
}
