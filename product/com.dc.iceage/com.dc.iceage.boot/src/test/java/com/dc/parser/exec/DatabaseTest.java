package com.dc.parser.exec;

import com.dc.infra.database.type.DatabaseType;
import com.dc.infra.spi.TypedSPILoader;
import com.dc.parser.exec.sql.SQLStatementParserEngine;
import com.dc.parser.exec.sql.SQLStatementParserEngineFactory;
import com.dc.parser.model.engine.CacheOption;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.statement.SQLStatement;
import org.junit.jupiter.api.Test;

class DatabaseTest {

    @Test
    void oracle() {
        String sql = "select t1.f, (select * from t3 where t3.id = t1.id) from t1 join t2 on aa.id=bb.id , t5, (select * FROM t4) t44 where EXISTS (select * from t5 where t5.id=t2.id);";
//        String sql = ORACLE.create_table;
        SQLStatement statement = test(DatabaseType.Constant.ORACLE, sql);
        System.out.println(statement);
    }

    @Test
    void mysql() {
        SQLStatement statement = test(DatabaseType.Constant.MYSQL, "SHOW COUNT(*) WARNINGS");
        System.out.println(statement);
    }
    @Test
    void cliclhouse() {
        SQLStatement statement = test(DatabaseType.Constant.CLICKHOUSE, "select * from A where A.id = 1");
        System.out.println(statement);
    }
    @Test
    void gaussdb() {
        SQLStatement statement = test(DatabaseType.Constant.GAUSSDB, "select * from A where A.id = 1");
        System.out.println(statement);
    }
    @Test
    void hetu() {
        SQLStatement statement = test(DatabaseType.Constant.HETU, "select * from A where A.id = 1");
        System.out.println(statement);
    }
    @Test
    void hive() {
        SQLStatement statement = test(DatabaseType.Constant.HIVE, "select * from A where A.id = 1");
        System.out.println(statement);
    }

    @Test
    void mg() {
        SQLStatement statement = test(DatabaseType.Constant.MONGO_DB, "db.collection.find( { } )");
        System.out.println(statement);
    }

    @Test
    void sqlServer() {
        SQLStatement statement = test(DatabaseType.Constant.SQL_SERVER, "select * from A where A.id = 1");
        System.out.println(statement);
    }
    @Test
    void pg() {
        SQLStatement statement = test(DatabaseType.Constant.PG_SQL, "VACUUM (VERBOSE, ANALYZE) onek");
        System.out.println(statement);
    }
    @Test
    void vertica() {
        SQLStatement statement = test(DatabaseType.Constant.VERTICA, "select sd.a from ta;");
        System.out.println(statement);
    }

    @Test
    void hana() {
        SQLStatement statement = test(DatabaseType.Constant.HANA, "SELECT t1.branch, case_join.c1, case_join.c2\n" +
                "FROM t1 LEFT OUTER MANY TO ONE CASE JOIN\n" +
                "      WHEN t1.branch = 1 THEN RETURN (c1, c2) FROM t2 ON t1.id = t2.id\n" +
                "    WHEN t1.branch = 2 THEN RETURN (c1, c2) FROM t3 ON t1.id = t3.id\n" +
                "    END AS case_join;");
        System.out.println(statement);
    }

    @Test
    void testRegisterSQLRule() {
        SQLRule example = TypedSPILoader.getService(SQLRule.class, "example");
        System.out.println(example);
    }

    public SQLStatement test(DatabaseType.Constant constant, String sql) {
        DatabaseType databaseType = TypedSPILoader.getService(DatabaseType.class, constant);
        SQLStatementParserEngine sqlStatementParserEngine = SQLStatementParserEngineFactory.getSQLStatementParserEngine(databaseType, new CacheOption(2000, 65535L), new CacheOption(64, 1024L));
        return sqlStatementParserEngine.parse(sql, true);
    }
}
