package com.dc.parser.model.statement.ddl;

import com.dc.parser.model.segment.generic.table.SimpleTableSegment;
import com.dc.parser.model.statement.AbstractSQLStatement;
import com.dc.parser.model.value.identifier.IdentifierValue;

public abstract class CreatePermissionStatement extends AbstractSQLStatement implements DDLStatement {


    public IdentifierValue getPermissionName() {
        return null;
    }

    public SimpleTableSegment getTable() {
        return null;
    }

    public IdentifierValue getCorrelationName() {
        return null;
    }
}
