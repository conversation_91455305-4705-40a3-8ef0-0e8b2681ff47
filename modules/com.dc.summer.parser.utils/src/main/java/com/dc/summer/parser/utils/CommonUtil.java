package com.dc.summer.parser.utils;

import com.dc.node.WhereCondition;
import com.dc.parser.elasticsearch.model.GetSqlStatement;
import com.dc.parser.mongodb.model.CursorStatement;
import com.dc.parser.mongodb.model.FindSqlStatement;
import com.dc.sqlparser.DCustomSqlStatement;
import com.dc.sqlparser.DStatementList;
import com.dc.sqlparser.nodes.TExpression;
import com.dc.sqlparser.nodes.TResultColumn;
import com.dc.sqlparser.nodes.TResultColumnList;
import com.dc.sqlparser.stmt.*;
import com.dc.sqlparser.stmt.mssql.TMssqlBlock;
import com.dc.sqlparser.stmt.mssql.TMssqlExecute;
import com.dc.sqlparser.stmt.mssql.TMssqlIfElse;
import com.dc.sqlparser.types.EExpressionType;
import com.dc.sqlparser.types.ESqlStatementType;
import com.dc.stmt.*;
import com.dc.summer.model.DBUtils;
import com.dc.summer.parser.sql.constants.*;
import com.dc.summer.parser.sql.model.SqlAuthModel;
import com.dc.summer.parser.sql.model.TableColumnModel;
import com.dc.summer.parser.sql.type.GrantType;
import com.dc.summer.parser.utils.model.*;
import com.dc.summer.parser.utils.type.SqlColumnType;
import com.dc.type.DatabaseType;
import com.dc.type.SceneType;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class CommonUtil {

    private CommonUtil() {}

    public static final String TIMESTAMP_PREFIX = "TIMESTAMP";

    public static final Set<String> DATE_TIME_TYPES = Set.of("DATE", "DATETIME", "TIMESTAMP");

    public static Gson gson = new Gson();

    public static String getObjectType(String sql, Integer dbType, CustomSqlStatement customSqlStatement) {
        if (DatabaseType.getIdentCode(DatabaseType.useDcSqlParser()).contains(dbType)) {
            return CommonUtil.getObjectType(customSqlStatement, dbType);
        } else {
            return CommonUtil.getObjectType(sql);
        }
    }

    public static String getObjectType(String sql) {

        String objectType = "";
        try {
            String pattern = "(?si)^\\s*(CREATE|ALTER|TRUNCATE|DROP|COMMENT|RENAME|EXECUTE|EXEC)\\s+" +
                    "(OR\\s+REPLACE\\s+|ON\\s+|GLOBAL\\s+|DEFINER\\s*=\\s*`\\s*[0-9a-zA-Z_]+\\s*`\\s*@\\s*`\\s*[0-9a-zA-Z%.]+\\s*`\\s*)?" +
                    "(TEMPORARY\\s+|PUBLIC\\s+|EXTERNAL\\s+|HUGE\\s+|CLUSTER\\s+|NOT\\s+PARTIAL\\s+|TRANSACTIONAL\\s+)?" +
                    "(UNIQUE\\s+|BITMAP\\s+|SPATIAL\\s+|CONTEXT\\s+|ARRAY\\s+|FLEX\\s+|DIMENSION\\s+|CLUSTERED\\s+)?" +
                    "(TABLESPACE|DATABASE\\s+LINK|DATABASE|SCHEMA|USER|PROCEDURE|PROC|TABLE|TRIGGER|MATERIALIZED\\s+VIEW|VIEW|FUNCTION|SEQUENCE|INDEX|SYNONYM|ALIAS|COLUMN|EVENT|PACKAGE\\s+BODY|PACKAGE|ROLE|TYPE|CLASS|DOMAIN|CONTEXT|DIRECTORY|DICTIONARY|CONNECTOR|TABLEGROUP|STATISTICS|SAVEPOINT)\\s+" +
                    "(IF\\s+)?(NOT\\s+)?(EXISTS\\s+)?" +
                    "([0-9a-zA-Z_\"'\\[\\]`.:]+)";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(sql);
            if (m.find()) {
                objectType = m.group(5);
                if ("proc".equalsIgnoreCase(objectType)) {
                    objectType = "PROCEDURE";
                } else if (objectType != null && "MATERIALIZEDVIEW".equalsIgnoreCase(objectType.replaceAll(" ", ""))) {
                    objectType = "VIEW";
                }
            }

        } catch (Exception e) {
            log.error("getObjectType error : ", e);
        }

        return objectType;
    }

    public static String getObjectType(CustomSqlStatement customSqlStatement, Integer dbType) {

        String objectType = "";
        try {
            if (Arrays.asList(DatabaseType.MONGODB.getValue(), DatabaseType.REDIS.getValue()).contains(dbType)) {
                if (customSqlStatement instanceof CreateSqlStatement) {
                    objectType = SqlConstant.KEY_CREATE_COLLECTION.equalsIgnoreCase(customSqlStatement.getOperation()) ? SqlConstant.KEY_TABLE
                            : SqlConstant.KEY_CREATE_VIEW.equalsIgnoreCase(customSqlStatement.getOperation()) ? SqlConstant.KEY_VIEW
                            : Arrays.asList(SqlConstant.KEY_CREATE_INDEX, SqlConstant.KEY_CREATE_INDEXES).contains(customSqlStatement.getOperation()) ? SqlConstant.KEY_INDEX : "";
                } else if (customSqlStatement instanceof AlterSqlStatement) {
                    objectType = SqlConstant.KEY_TABLE;
                } else if (customSqlStatement instanceof DropSqlStatement) {
                    objectType = SqlConstant.KEY_DROP.equalsIgnoreCase(customSqlStatement.getOperation()) ? RedisSqlConstant.KEY_TABLE_OR_VIEW
                            : Arrays.asList(SqlConstant.KEY_DROP_INDEX, SqlConstant.KEY_DROP_INDEXES).contains(customSqlStatement.getOperation()) ? SqlConstant.KEY_INDEX
                            : SqlConstant.KEY_DROP_DATABASE.equals(customSqlStatement.getOperation()) ? SqlConstant.KEY_DATABASE
                            : "";
                }
            } else if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType)) {
                if (customSqlStatement != null && customSqlStatement.getObjectName() != null && StringUtils.isNotBlank(customSqlStatement.getObjectName().getType())) {
                    objectType = customSqlStatement.getObjectName().getType();
                }
            }
        } catch (Exception e) {
            log.error("getObjectType error : ", e);
        }

        return objectType;
    }

    public static DCustomSqlStatement getStatement(String sql, Integer dbType) {
        try {
            sql = sql.replaceAll("(?i)\\s*IF\\s+EXISTS\\s*", " ");
            DStatementList sqlStatements = SqlSplitUtil.split(sql, dbType);
            if (sqlStatements.size() == 1) {
                return sqlStatements.get(0);
            } else {
                return null;
            }

        } catch (Exception e) {
            log.error("getStatement error : ", e);
        }

        return null;
    }

    public static String getTrueCatalogName(Integer dbType, String[] split, String defaultCatalogName) {
        String catalogName = "";
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
            if (split.length == 3) {
                catalogName = split[0];
            } else {
                catalogName = defaultCatalogName;
            }
        }
        return catalogName;
    }

    public static String getTrueSchemaName(Integer dbType, String[] split, String defaultSchemaName) {
        String schemaName;
        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
            if (split.length == 3) {
                schemaName = split[1];
            } else if (split.length == 2) {
                schemaName = split[0];
            } else {
                schemaName = defaultSchemaName;
            }
        } else if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (split.length == 3) {
                schemaName = split[0];
            } else {
                schemaName = defaultSchemaName;
            }
        } else if (DatabaseType.getHasPackageDatabaseList().contains(dbType)){ //oracle、DM三段式 *.*.*
            if (split.length == 3){
                schemaName = split[0];
            } else if (split.length == 2){
                schemaName = split[0];
            } else {
                schemaName = defaultSchemaName;
            }
        } else if (split.length == 2) {
            schemaName = split[0];
        } else {
            schemaName = defaultSchemaName;
        }
        return schemaName;
    }

    public static String getTrueFrameworkName(Integer dbType, String[] split, String defaultFrameworkName) {
        String frameworkName = "";
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (split.length == 3) {
                frameworkName = split[1];
            } else if (split.length == 2) {
                frameworkName = split[0];
            } else {
                frameworkName = defaultFrameworkName;
            }
        }
        return frameworkName;
    }

    public static String getTrueTableName(Integer dbType, String[] split) {
        String tableName;
        if (CommonUtil.useColonSplit(dbType)) {
            String[] split1 = split[split.length - 1].split("\\.");
            tableName = split1[split1.length - 1];
        } else if (DatabaseType.ORACLE.getValue().equals(dbType) && split.length == 3){ //针对Oracle包对象
            tableName = split[split.length - 2];
        } else {
            tableName = split[split.length - 1];
        }
        return tableName;
    }

    public static String replace(String name) {
        if (null == name) {
            return null;
        }
        return name.replaceAll("\"|'|`|\\[|\\]", "");
    }

    public static String replaceDefaultUpper(String name) {
        if (name == null) {
            return null;
        }
        String replace = replace(name);
        return name.equals(replace) ? replace.toUpperCase(Locale.ROOT) : replace;
    }

    public static String replaceSchemaName(String name) {
        if (null == name) {
            return null;
        }

        try {
            if (name.contains("--") && (name.endsWith("\n") || name.endsWith("\r") || name.endsWith("\r\n"))) {
                name = name.replaceFirst("--.*?(\n|\r|\r\n)$", "").trim();
            }
            if (name.contains("/*") && name.contains("*/")) {
                name = name.replaceAll("/\\*[\\s\\S]*?\\*/", "").trim();
            }
        } catch (Exception e) {
            log.error("去除schemaName中的注释错误", e);
        }

        return replace(name);
    }

    public static String replacedDoubleQuotationMarks(String name) {
        if (null == name) {
            return null;
        }
        if (name.length() > 2 && name.startsWith("\"") && name.endsWith("\"")) {
            return name.substring(1, name.length() - 1);
        }
        return replace(name);
    }

    public static String replacedSingleQuotationMarks(String name) {
        if (null == name) {
            return null;
        }
        if (name.length() > 2 && name.startsWith("'") && name.endsWith("'")) {
            return name.substring(1, name.length() - 1);
        }
        return replace(name);
    }

    //用于parser处理列值
    public static String replaceParserColumnValue(String result, boolean needReplaceTransferSymbol) {
        if (null == result) {
            return null;
        }
        if (result.length() != 0 && result.charAt(0) == '\'' && result.charAt(result.length() - 1) == '\'') {
            result = result.substring(1, result.length() - 1);
            if (needReplaceTransferSymbol) {
                result = result.replaceAll("''", "'");
            }
        } else if (result.length() != 0 && result.charAt(0) == '\"' && result.charAt(result.length() - 1) == '\"') {
            result = result.substring(1, result.length() - 1);
        }
        return result;
    }

    public static String buildWhereClause(List<Map<String, String>> insertKeyValues, List<TableColumnModel> tableColumns, Integer dbType, List<String> primaryColumnUpper) {

        List<StringBuilder> whereOrClause = Stream.generate(StringBuilder::new)
                .limit(insertKeyValues.size())
                .collect(Collectors.toList());

        DatabaseType databaseType = DatabaseType.of(dbType);

        for (int i = 0; i < insertKeyValues.size(); i++) {
            Map<String, String> insertKeyValue = insertKeyValues.get(i);

            for (Map.Entry<String, String> entry : insertKeyValue.entrySet()) {

                Optional<TableColumnModel> tableColumnModelOptional = tableColumns.stream()
                        .filter(columnModel -> columnModel.getColumnName().equalsIgnoreCase(entry.getKey()))
                        .findFirst();

                if (tableColumnModelOptional.isEmpty()) {
                    continue;
                }

                // 取真实列名
                TableColumnModel columnModel = tableColumnModelOptional.get();

                String columnName = columnModel.getColumnName();
                if (List.of(DatabaseType.MYSQL, DatabaseType.OCEAN_BASE_MYSQL, DatabaseType.TDMYSQL).contains(databaseType)) {
                    columnName = String.format("`%s`", columnName);
                    if ("JSON".equalsIgnoreCase(columnModel.getColumnType())) {
                        columnName = String.format("JSON_UNQUOTE(%s)", columnName);
                    }
                } else if (List.of(DatabaseType.ORACLE, DatabaseType.OCEAN_BASE_ORACLE, DatabaseType.DM).contains(databaseType)) {
                    columnName = String.format("\"%s\"", columnName);
                } else if (Objects.equals(DatabaseType.SQL_SERVER, databaseType)) {
                    columnName = String.format("[%s]", columnName);
                }

                String columnValue = entry.getValue();

                //主键为空，不拼写where条件
                boolean pkIsNull = primaryColumnUpper.stream().anyMatch(pk -> pk.equalsIgnoreCase(entry.getKey())) &&
                        StringUtils.isBlank(columnValue);

                if (pkIsNull) {
                    continue;
                }

                StringBuilder orClause = whereOrClause.get(i);
                if (orClause.length() != 0) {
                    orClause.append(" and ");
                }
                orClause.append(columnName);
                orClause.append(columnValue == null ? " is " : " = ");

                boolean needQuote = columnValue != null &&
                        requiresQuotingForDbSpecifics(databaseType, columnValue,  columnModel) &&
                        !isTimestampTypeWithValuePrefix(columnModel, columnValue);
                if (needQuote) {
                    orClause.append("'");
                }
                orClause.append(columnValue);
                if (needQuote) {
                    orClause.append("'");
                }
            }
        }

        return "where " + whereOrClause.stream()
                .map(stringBuilder -> stringBuilder.insert(0, "(").append(")").toString())
                .reduce((s1, s2) -> s1 + " or " + s2)
                .orElse("1 = 1");
    }

    /**
     * 检查基于数据库类型的特定引用规则。
     * 当前：如果不是 SQL Server 的某个特殊命令，则认为需要引用（基于此规则）。
     *
     * @return true 如果基于数据库规则需要引用，false 如果是特殊情况不需要引用。
     */
    public static boolean requiresQuotingForDbSpecifics(DatabaseType databaseType, String columnValue, TableColumnModel columnModel) {
        if (columnModel != null && SqlColumnType.getNumericTypes().contains(columnModel.getDataType().toLowerCase())) {
            return false;
        }
        // 如果不是 SQL Server，则此规则不适用（即不属于“不需要引号”的特例）
        if (databaseType != DatabaseType.SQL_SERVER) {
            return true;
        }
        // 是 SQL Server，检查是否是那个特殊的不需要引号的命令
        boolean isSpecialNoQuoteCommand = StringUtils.isNotBlank(columnValue) && sqlserverlCommandN(columnValue); // 假设 sqlserverlCommandN 存在且功能明确
        return !isSpecialNoQuoteCommand; // 如果是特殊命令，则返回 false (不需要引号); 否则返回 true (需要引号)
    }

    /**
     * 检查列是否为日期/时间类型，并且其值是否以 "TIMESTAMP" 开头。
     *
     * @return true 如果类型和前缀都匹配。
     */
    public static boolean isTimestampTypeWithValuePrefix(TableColumnModel columnModel, String columnValue) {
        return isDateTimeDataType(columnModel) && columnValue.toUpperCase().startsWith(TIMESTAMP_PREFIX);
    }

    /**
     * 检查列的数据类型是否为日期或时间相关类型。
     *
     * @return true 如果是指定的日期/时间类型之一。
     */
    private static boolean isDateTimeDataType(TableColumnModel columnModel) {
        String dataType = columnModel.getDataType().toUpperCase();
        return DATE_TIME_TYPES.contains(dataType);
    }


    //针对Sqlserver适配 <N' '> 语法
    private static boolean sqlserverlCommandN(String value) {
        String regex = "^N'.*'$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        if (matcher.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static String[] getDdlObjectName(SqlDdlResult sqlDdlResultModel, Integer dbType, String objectType) {
        String[] objectName = sqlDdlResultModel.getObjectName().split("\\.");
        if (CommonUtil.useColonSplit(dbType)) {
            objectName = sqlDdlResultModel.getObjectName().split(":");
            if (SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType)) {
                objectName = sqlDdlResultModel.getObjectName().split("\\.");
                if (objectName.length > 1) {
                    String[] objectNameTemp = new String[objectName.length - 1];
                    for (int i = 0; i < objectName.length - 1; i++) {
                        objectNameTemp[i] = objectName[i];
                    }
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < objectNameTemp.length; i++) {
                        sb.append(objectNameTemp[i]).append(".");
                    }
                    objectName = sb.substring(0, sb.length() - 1).split(":");
                }
            }
        }
        return objectName;
    }

    public static List<String> getDDLArray(String[] objectName, String objectType, Integer dbType, String schemaName, String catalogName, String originalObjectName) {
        List<String> array = new ArrayList<>(Arrays.asList(objectName));

        if (SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType) &&
                Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue(), DatabaseType.DM.getValue(), DatabaseType.DB2.getValue()).contains(dbType)) {
            if (array.size() != 3) {
                array.add(0, schemaName);
            }
        } else if (SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType) && DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
            if (array.size() == 3) {
                array.add(0, catalogName);
            } else if (array.size() == 2) {
                array.add(0, schemaName);
                array.add(0, catalogName);
            }
        }
        if (!SqlConstant.KEY_COLUMN.equalsIgnoreCase(objectType)) {
            if (StringUtils.isNotBlank(originalObjectName) && !originalObjectName.startsWith(".") && originalObjectName.endsWith(".")) {
                array.add("");
            }
        }
        return array;
    }

    public static MatchParseModel dealMatch(List<String> array, Integer dbType, String frameworkName, String schemaName, String catalogName) {
        MatchParseModel matchPares = new MatchParseModel();

        if (DatabaseType.getCatalogDatabaseIntegerValueList().contains(dbType)) {
            if (array.size() == 1) {
                matchPares.setObjectName(array.get(0));
            } else if (array.size() == 2) {
                matchPares.setObjectName(array.get(1));
                matchPares.setSchemaName(array.get(0).trim().isEmpty() ? schemaName : array.get(0).trim());
            } else {
                matchPares.setObjectName(array.get(2));
                matchPares.setSchemaName(array.get(1).trim().isEmpty() ? schemaName : array.get(1).trim());
                matchPares.setCatalogName(array.get(0).trim().isEmpty() ? catalogName : array.get(0).trim());
            }
        } else if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            if (array.size() == 1) {
                matchPares.setObjectName(array.get(0));
                matchPares.setFrameworkName(frameworkName);
            } else if (array.size() == 2) {
                matchPares.setObjectName(array.get(1));
                matchPares.setFrameworkName(array.get(0).trim().isEmpty() ? frameworkName : array.get(0).trim());
            } else {
                matchPares.setObjectName(array.get(2));
                matchPares.setFrameworkName(array.get(1).trim().isEmpty() ? frameworkName : array.get(1).trim());
                matchPares.setSchemaName(array.get(0).trim().isEmpty() ? schemaName : array.get(0).trim());
            }
        } else if (CommonUtil.useColonSplit(dbType)) {
            if (array.size() > 1) {
                matchPares.setSchemaName(array.get(0));
            }
            String[] split = array.get(array.size() - 1).split("\\.");
            matchPares.setObjectName(split[split.length - 1]);
        } else if (DatabaseType.ADBMYSQL2.getValue().equals(dbType)) {
            if (array.size() == 3) {
                matchPares.setObjectName(array.get(2));
                matchPares.setSchemaName(array.get(1));
            } else if (array.size() == 2) {
                matchPares.setObjectName(array.get(1));
                matchPares.setSchemaName(array.get(0));
            } else {
                matchPares.setObjectName(array.get(0));
            }
        } else {
            if (array.size() > 1) {
                matchPares.setObjectName(array.get(1));
                matchPares.setSchemaName(array.get(0));
            } else {
                matchPares.setObjectName(array.get(0));
            }
        }

        return matchPares;
    }

    public static Set<String> getDCSupportFunctions() {
        Set<String> allFunctions = new LinkedHashSet<>();
        // dc支持的函数
        for (GrantType gt : GrantConstant.FunTarget) {
            allFunctions.add(gt.getValue());
        }
        return allFunctions;
    }

    public static boolean isStaFunc(String funcName){
        return CommonUtil.getDCSupportFunctions().contains(funcName.toUpperCase(Locale.ROOT));
    }

    public static Set<String> getFunctions(Set<String> oldFunctions) {
        Set<String> functions = new LinkedHashSet<>();
        if (oldFunctions != null) {
            for (String function : oldFunctions) {
                if (getDCSupportFunctions().contains(function.toUpperCase())) {
                    functions.add(function);
                }
            }
        }
        return functions;
    }

    public static Set<String> getNotDCSupportFunctions(Set<String> oldFunctions) {
        Set<String> functions = new LinkedHashSet<>();
        if (oldFunctions != null) {
            for (String function : oldFunctions) {
                if (!getDCSupportFunctions().contains(function.toUpperCase())) {
                    functions.add(function);
                }
            }
        }
        return functions;
    }

    public static boolean isNumeric(String str) {
        // 验证是否为空
        if (str == null) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public static boolean hasSameFunction(Integer dbType, SqlParseModel sqlParserModel, String operation) {

        if (SqlConstant.KEY_CALL.equalsIgnoreCase(operation) || sqlParserModel.getAction().isSelectFunction()) {

            if (DatabaseType.getPGSqlIntegerValueList().contains(dbType)) {
                return true;
            } else if (CommonUtil.useColonSplit(dbType)) {
                return true;
            } else if (Arrays.asList(DatabaseType.DB2.getValue(), DatabaseType.DB2AS400.getValue()).contains(dbType)) {
                return true;
            }

        }

        return false;
    }

    public static boolean dictionaryContains(List<String> permissionsDictionary, String operation) {
        boolean contains = false;
        operation = CommonUtil.transformOperation(operation);
        if (permissionsDictionary != null && permissionsDictionary.size() > 0) {
            if (permissionsDictionary.contains(operation.toLowerCase(Locale.ROOT))) {
                contains = true;
            }
        }

        return contains;
    }

    public static List<Map<String, Object>> buildCannotGrantedMap() {
        List<Map<String, Object>> noPermissionObjects = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("message", "没有找到符合的权限类型，此类权限无法通过操作权限进行授予!");
        noPermissionObjects.add(map);
        return noPermissionObjects;
    }

    public static int canEdit(SqlParseModel sqlParserModel) {

        // explain select/select function/select sequence不查询主键
        if (sqlParserModel.getAction().getExplainOperation() != null
                || sqlParserModel.getAction().isSelectFunction()
                || sqlParserModel.getAction().isSelectSequence()
                || sqlParserModel.getAction().isSelectValue()) {
            return SceneType.READ_ONLY.getValue();
        }

        // 判断单表
        List<SqlAuthModel> sqlAuthModelList = sqlParserModel.getSqlAuthModelList()
                .stream()
                .filter(sqlAuthModel -> SqlConstant.KEY_TABLE.equals(sqlAuthModel.getType()))
                .collect(Collectors.toList());
        if (sqlAuthModelList.size() == 1) {
            String singleTable = sqlAuthModelList.get(0).getName();
            if (singleTable.contains("@")) {
                return SceneType.DBLINK.getValue();
            }
            if ("DUAL".equalsIgnoreCase(singleTable)) {
                return SceneType.DUAL.getValue();
            }
        } else if (sqlAuthModelList.size() > 1) {
            Set<SqlAuthModel> sqlAuthModelSet = new HashSet<>(sqlAuthModelList);
            return sqlAuthModelSet.size() > 1 ? SceneType.MULTI_TABLE.getValue() : SceneType.JOIN_SELF.getValue();
        } else {
            return SceneType.READ_ONLY.getValue();
        }

        // from多表、带有join的不可编辑; join自身不可编辑; from相同表不可编辑
        DCustomSqlStatement tCustomSqlStatement = sqlParserModel.gettCustomSqlStatement();
        if (tCustomSqlStatement != null) {
            if (tCustomSqlStatement.getTables() != null && tCustomSqlStatement.getTables().size() > 1) {
                return SceneType.JOIN_SELF.getValue();
            }
            // 带有case when 的不可编辑
            if (tCustomSqlStatement.getResultColumnList() != null) {
                TResultColumnList tResultColumns = tCustomSqlStatement.getResultColumnList();
                for (TResultColumn tResultColumn : tResultColumns) {
                    if (tResultColumn.getExpr() != null
                            && tResultColumn.getExpr().getExpressionType() != null
                            && tResultColumn.getExpr().getExpressionType() == EExpressionType.case_t) {
                        return SceneType.CASE_WHEN.getValue();
                    }
                }
            }
        }

        // select union 不可编辑
        if (tCustomSqlStatement instanceof TSelectSqlStatement) {
            TSelectSqlStatement selectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;
            if (selectSqlStatement.getSetOperatorType() != null &&
                    "union".equalsIgnoreCase(selectSqlStatement.getSetOperatorType().toString())) {
                return SceneType.SELECT_UNION.getValue();
            } else if (selectSqlStatement.getLeftStmt() != null && selectSqlStatement.getRightStmt() != null) {
                return SceneType.MULTI_TABLE.getValue();
            }
        }

        // 带有函数和group by子句的不可编辑,带有order by的可以编辑
        // 翻译 ：select语句，结果集列带有函数、语句含有group by子句的不可编辑，但是语句带有order by的可以编辑（后面这个条件默认成立，不需要说出来）
//        Set<String> functions = sqlParserModel.getAction().getFunctions();
//        if (functions != null) {
//            for (String fun : functions) {
//                if (!GrantType.ORDER.getValue().equalsIgnoreCase(fun) && !GrantType.DISTINCT.getValue().equalsIgnoreCase(fun)) {
//                    return SceneType.FUNCTION.getValue();
//                }
//            }
//        }
        boolean resultColumnsExistFunction = resultColumnsExistFunction(tCustomSqlStatement);
        boolean existsGroupBy = false;
        Set<String> functions = sqlParserModel.getAction().getFunctions();
        if (CollectionUtils.isNotEmpty(functions)) {
            existsGroupBy = functions.stream().anyMatch(f -> GrantType.GROUP.getValue().equalsIgnoreCase(f));
        }
        if (resultColumnsExistFunction || existsGroupBy) {
            return SceneType.FUNCTION.getValue();
        }

        return SceneType.CAN_EDIT.getValue();
    }

    public static boolean resultColumnsExistFunction(DCustomSqlStatement statement) {
        if (statement instanceof TSelectSqlStatement) {
            boolean exist = false;
            TSelectSqlStatement selectStatement = (TSelectSqlStatement) statement;
            TResultColumnList resultColumnList = selectStatement.getResultColumnList();
            if (ObjectUtils.isNotEmpty(resultColumnList)) {
                for (TResultColumn r : resultColumnList) {
                    if (r.getExpr() != null && r.getExpr().getFunctionCall() != null && r.getExpr().getFunctionCall().getFunctionName() != null) {
                        exist = true;
                        break;
                    }
                }
            }
            return exist;
        }
        return false;
    }

    public static List<ActionModel> getActions(Integer dbType, SqlParseModel sqlParserModel) {
        List<ActionModel> actionList = new ArrayList<>();

        for (SqlAuthModel sqlAuthModel : sqlParserModel.getSqlAuthModelList()) {
            ActionModel actionModel = new ActionModel();

            final String operation = sqlAuthModel.getOperation();
            // STATISTICS 是衍生出来的子动作
            if (SqlConstant.STATISTICS.equals(operation)) {
                continue;
            }

            actionModel.setObjectType(sqlAuthModel.getType());
            actionModel.setOperation(operation);

            String originName = sqlAuthModel.getOriginName();
            String objectName = CommonUtil.isTransformationTableName(originName) ?
                    sqlAuthModel.getFullName() : CommonUtil.getTransformationTableName(originName, dbType);
            String newObjectName = sqlAuthModel.getNewName();
            String schemaName = sqlAuthModel.getFullSchemaName();

            if (SqlConstant.KEY_USER.equalsIgnoreCase(sqlAuthModel.getType())
                    && Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER).contains(operation)) {
                schemaName = "";
                if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue()).contains(dbType)) {
                    objectName = objectName.toUpperCase(Locale.ROOT);
                }
            } else if (sqlAuthModel.getType() != null
                    && Arrays.asList(SqlConstant.KEY_SCHEMA, SqlConstant.KEY_DATABASE).contains(sqlAuthModel.getType().toUpperCase(Locale.ROOT))
                    && Arrays.asList(SqlConstant.KEY_CREATE, SqlConstant.KEY_DROP, SqlConstant.KEY_ALTER).contains(operation)) {
                schemaName = "";
                if (DatabaseType.SQL_SERVER.getValue().equals(dbType) && StringUtils.isNotBlank(objectName)) {
                    String[] split = objectName.split("\\.");
                    objectName = split[split.length - 1];
                }
            } else if (sqlAuthModel.getType() != null
                    && SqlConstant.KEY_DATABASE.equals(sqlAuthModel.getType().toUpperCase(Locale.ROOT))
                    && SqlConstant.KEY_USE.equals(operation)) {
                schemaName = "";
            } else if (SqlConstant.KEY_ANONYMOUS_BLOCK.equalsIgnoreCase(sqlAuthModel.getType()) && SqlConstant.KEY_BEGIN_END.equalsIgnoreCase(operation)) {
                schemaName = "";
            }

            if (DatabaseType.getIdentCode(DatabaseType.getNeedCheckShowDictionary()).contains(dbType)
                    && SqlConstant.KEY_SHOW.equalsIgnoreCase(operation)
                    && Arrays.asList(DataDictionaryConstant.MYSQL_DATA_DICTIONARY).contains(sqlAuthModel.getName().toLowerCase(Locale.ROOT))) {
                schemaName = "";
                objectName = objectName.toLowerCase(Locale.ROOT);
            } else if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.OCEAN_BASE_ORACLE.getValue()).contains(dbType)
                    && Arrays.asList(DataDictionaryConstant.ORACLE_DATA_DICTIONARY).contains(objectName.toUpperCase(Locale.ROOT))) {
                schemaName = "";
                objectName = objectName.toUpperCase(Locale.ROOT);
            } else if (DatabaseType.DM.getValue().equals(dbType)
                    && Arrays.asList(DataDictionaryConstant.DM_DATA_DICTIONARY).contains(objectName.toUpperCase(Locale.ROOT))) {
                schemaName = "";
                objectName = objectName.toUpperCase(Locale.ROOT);
            } else if (DatabaseType.getIdentCode(DatabaseType.getNeedCheckPGSqlDictionary()).contains(dbType)
                    && Arrays.asList(DataDictionaryConstant.PG_SQL_DATA_DICTIONARY).contains(objectName.toLowerCase(Locale.ROOT))) {
                schemaName = "";
                objectName = objectName.toLowerCase(Locale.ROOT);
            } else if (DatabaseType.KING_BASE.getValue().equals(dbType)) {
                if (Arrays.asList(DataDictionaryConstant.KINGBASE_DATA_DICTIONARY).contains(objectName.toLowerCase(Locale.ROOT))
                        || Arrays.asList(DataDictionaryConstant.PG_SQL_DATA_DICTIONARY).contains(objectName.toLowerCase(Locale.ROOT))) {
                    schemaName = "";
                    objectName = objectName.toLowerCase(Locale.ROOT);
                }
            }

            if (DatabaseType.ELASTIC_SEARCH.getValue().equals(dbType)) {
                schemaName = ""; // es没有schema
            }

            actionModel.setSchemaName(schemaName);
            actionModel.setObjectName(objectName);
            actionModel.setNewObjectName(newObjectName);
            actionModel.setDblinkName(sqlAuthModel.getDblinkName());

            actionList.add(actionModel);

        }

        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(sqlParserModel.getOperation()) && sqlParserModel.getSqlAuthModelList().isEmpty() && sqlParserModel.getAction().getTables().isEmpty()) {
            actionList.clear();
            ActionModel actionModel = new ActionModel();
            actionModel.setOperation(sqlParserModel.getOperation());
            actionList.add(actionModel);
        }

        if (Arrays.asList(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE).contains(sqlParserModel.getOperation())
                || (SqlConstant.KEY_SET.equalsIgnoreCase(sqlParserModel.getOperation()) && !sqlParserModel.isSwitchDatabase())) {
            actionList.clear();
            ActionModel actionModel = new ActionModel();
            actionModel.setOperation(sqlParserModel.getOperation());
            actionList.add(actionModel);
        }

        if (sqlParserModel.getSqlAuthModelList().isEmpty() && sqlParserModel.getAction().isContainsDBLink() && sqlParserModel.getAction().getDblinkTables() != null) {
            for (String table : sqlParserModel.getAction().getDblinkTables()) {
                ActionModel actionModel = new ActionModel();
                actionModel.setObjectName("TABLE");
                actionModel.setOperation(sqlParserModel.getOperation());
                if (StringUtils.isNotBlank(table)) {
                    String[] objectName = table.split("\\.");
                    if (objectName.length > 1) {
                        actionModel.setSchemaName(objectName[0]);
                    }
                    actionModel.setObjectName(objectName[objectName.length - 1]);
                }
                actionList.add(actionModel);
            }
        }

        return actionList;
    }

    public static Map<String, List<String>> mergeActionList(List<ActionModel> actionList, Integer dbType) {
        if (actionList == null) {
            return null;
        }
        return actionList.stream()
                .collect(Collectors.groupingBy(ActionModel::getOperation, Collectors.mapping(actionModel -> {
                            String structSeparator = CommonUtil.useColonSplit(dbType) || DatabaseType.H_BASE.getValue().equals(dbType) ? ":" : ".";
                            return DBUtils.getConcatName(structSeparator, actionModel.getDblinkName(), actionModel.getSchemaName(), actionModel.getObjectName());
                        },
                        Collectors.toList())));
    }

    public static boolean isDefaultSchema(Set<String> tables) {
        for (String table : tables) {
            String[] split = table.split("\\.");
            if (split.length == 1) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getCheckOperations(String operation, String ddlSubdivide) {
        List<String> checkOperations = new ArrayList<>();
        checkOperations.add(operation);
        if (SqlConstant.KEY_SELECT.equalsIgnoreCase(operation)) {
            checkOperations.add(OperationAuthConstant.statistics);
        } else if (StringUtils.isNotBlank(ddlSubdivide)) {
            checkOperations.add(ddlSubdivide);
        } else if (SqlConstant.KEY_COMMENT.equalsIgnoreCase(operation)) {
            checkOperations.addAll(Arrays.asList("ALTER", "CREATE", "ALTER_TABLE", "CREATE_TABLE"));
        }
        return checkOperations;
    }

    public static Set<String> buildNeedOtherOperations(Set<String> oldSet, String operation) {
        Set<String> newSet = new LinkedHashSet<>();
        if (oldSet != null) {
            newSet.addAll(oldSet);
        }
        newSet.add(operation);
        if (OperationAuthConstant.select.equalsIgnoreCase(operation)) {
            newSet.add(OperationAuthConstant.statistics);
        }
        return newSet;
    }

    public static String implode(List<String> list, DbTypeRelatedModel dbTypeRelatedModel) {
        StringBuilder sb = new StringBuilder();
        String delimiter = dbTypeRelatedModel.getDelimiter();
        boolean isDualWhite = dbTypeRelatedModel.isDualWhite();

        // 去重
        Set<String> set = new LinkedHashSet<>(list);
        list.clear();
        list.addAll(set);

        for (String str : list) {
            if (isDualWhite && SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(str.substring(str.lastIndexOf(delimiter) + 1))) {
                continue;
            }
            if (StringUtils.isEmpty(str) || str.endsWith(delimiter)) {
                continue;
            }
            sb.append(",").append(str);
        }

        if (sb.length() > 1) {
            return sb.substring(1);
        } else {
            return "";
        }
    }

    public static boolean isSuccess(Map<String, List<String>> operation, String token) {

        try {
            Set<String> auth = new HashSet<>();

            for (Map.Entry<String, List<String>> entry : operation.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(GrantType.manageTarget.getValue())) {
                    auth.add(GrantType.COMMENT.getValue());
                    auth.addAll(entry.getValue());
                } else {
                    auth.addAll(entry.getValue());
                }
            }

            token = CommonUtil.transformOperation(token);

            for (String str : auth) {
                if (str.equalsIgnoreCase(token)) {
                    return true;
                }
            }

        } catch (Exception ignore) {
        }

        return false;
    }

    public static boolean isSuccessForFunc(Map<String, List<String>> operation, String token1, String token2) {

        try {
            Set<String> auth = new HashSet<>();

            for (Map.Entry<String, List<String>> entry : operation.entrySet()) {
                if (entry.getKey().equalsIgnoreCase(GrantType.manageTarget.getValue())) {
                    auth.add(GrantType.COMMENT.getValue());
                    auth.addAll(entry.getValue());
                } else {
                    auth.addAll(entry.getValue());
                }
            }

            token1 = CommonUtil.transformOperation(token1);
            token2 = CommonUtil.transformOperation(token2);

            for (String str : auth) {
                if (str.equalsIgnoreCase(token1) || str.equalsIgnoreCase(token2)) {
                    return true;
                }
            }

        } catch (Exception ignore) {
        }

        return false;
    }

    public static boolean specialCasePass(Map<String, List<String>> operations, String operation, List<String> permissionsDictionary) {
        boolean pass = CommonUtil.isSuccess(operations, operation); // 验证通过,则说明是特例放行的operation
        if (pass) {
            pass = CommonUtil.dictionaryContains(permissionsDictionary, operation); // 是否权限字典包含当前操作
        }
        if (!pass && SqlConstant.KEY_SELECT.equalsIgnoreCase(operation)) {
            pass = CommonUtil.isSuccess(operations, OperationAuthConstant.statistics); // 操作统计权限也可放行select
            if (pass) {
                pass = CommonUtil.dictionaryContains(permissionsDictionary, OperationAuthConstant.statistics);
            }
        }
        return pass;
    }

    public static String transformOperation(String operation) {

        if (Arrays.asList(SqlConstant.KEY_VALUES, SqlConstant.KEY_EXEC, SqlConstant.KEY_EXECUTE).contains(operation)) {
            operation = SqlConstant.KEY_CALL;
        } else if (SqlConstant.KEY_RENAME.equalsIgnoreCase(operation)) {
            operation = SqlConstant.KEY_ALTER;
        } else if (Arrays.asList(SqlConstant.KEY_LOAD, SqlConstant.KEY_REPLACE, SqlConstant.KEY_IMPORT).contains(operation)) {
            operation = SqlConstant.KEY_INSERT;
        } else if (SqlConstant.KEY_DESCRIBE.equalsIgnoreCase(operation)) {
            operation = SqlConstant.KEY_DESC;
        }

        return operation;
    }

    public static boolean getShowWhereClause(String sql) {
        boolean hasWhereClause = false;
        try {
            String pattern = "(SHOW)\\s+(FULL\\s+)?(TABLES|COLUMNS|INDEX|TRIGGERS|TABLE\\s+STATUS|FIELDS)\\s+(FROM|IN)\\s+([0-9a-zA-Z_\"'\\[\\]`.$]+)\\s*(FROM|WHERE)?\\s*([0-9a-zA-Z_\"'\\[\\]`.$]+)?";
            Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(sql);
            if (m.find()) {
                if ("WHERE".equalsIgnoreCase(m.group(6))) {
                    hasWhereClause = true;
                }
            }
        } catch (Exception e) {
            log.error("getShowWhereClause error : ", e);
        }
        return hasWhereClause;
    }

    public static boolean containsDBLink(Set<String> tables, Integer dbType) {

        if (tables != null && Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(dbType)) {
            for (String table : tables) {
                String[] split = table.split("\\.");
                if (split[split.length - 1].contains("@")) {
                    return true;
                }
            }
        }

        return false;
    }

    public static Set<String> getDblinkTables(Set<String> tables, Integer dbType) {
        Set<String> dblinkTables = new HashSet<>();
        if (tables != null && Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(dbType)) {
            for (String table : tables) {
                String[] split = table.split("\\.");
                if (split[split.length - 1].contains("@")) {
                    dblinkTables.add(replace(table));
                }
            }
        }
        return dblinkTables;
    }

    public static boolean hasWhereClause(Integer dbType, DCustomSqlStatement tCustomSqlStatement, CustomSqlStatement customSqlStatement) {

        if (tCustomSqlStatement != null && tCustomSqlStatement.getWhereClause() != null && StringUtils.isNotBlank(tCustomSqlStatement.getWhereClause().toString())) {
            return true;
        } else if (Arrays.asList(DatabaseType.MONGODB.getValue(), DatabaseType.ELASTIC_SEARCH.getValue()).contains(dbType)) {
            if (customSqlStatement != null && customSqlStatement.getWhereCondition() != null
                    && !SqlConstant.KEY_BRACE.equals(customSqlStatement.getWhereCondition().getStringValue())) {
                return true;
            }
        } else if (hasSubQueryWhere(tCustomSqlStatement)) {
            return true;
        }

        return false;
    }

    public static boolean hasSubQueryWhere(DCustomSqlStatement tCustomSqlStatement) {
        TSelectSqlStatement tSelectSqlStatement = null;
        if (tCustomSqlStatement instanceof TSelectSqlStatement) {
            tSelectSqlStatement = (TSelectSqlStatement) tCustomSqlStatement;
        } else if (tCustomSqlStatement instanceof TCreateTableSqlStatement) {
            TCreateTableSqlStatement tCreateTableSqlStatement = (TCreateTableSqlStatement) tCustomSqlStatement;
            tSelectSqlStatement = tCreateTableSqlStatement.getSubQuery();
        } else if (tCustomSqlStatement instanceof TInsertSqlStatement) {
            TInsertSqlStatement tInsertSqlStatement = (TInsertSqlStatement) tCustomSqlStatement;
            tSelectSqlStatement = tInsertSqlStatement.getSubQuery();
        } else if (tCustomSqlStatement instanceof TCreateViewSqlStatement) {
            TCreateViewSqlStatement createViewSqlStatement = (TCreateViewSqlStatement) tCustomSqlStatement;
            tSelectSqlStatement = createViewSqlStatement.getSubquery();
        } else if (tCustomSqlStatement instanceof TExplainPlan) {
            TExplainPlan tExplainPlan = (TExplainPlan) tCustomSqlStatement;
            if (tExplainPlan.getStatement() instanceof TSelectSqlStatement) {
                tSelectSqlStatement = (TSelectSqlStatement) tExplainPlan.getStatement();
            }
        }

        return hasSelectWhere(tSelectSqlStatement);
    }

    public static boolean updateHasSelectWhere(DCustomSqlStatement tCustomSqlStatement) {
        if (tCustomSqlStatement instanceof TUpdateSqlStatement) {
            TUpdateSqlStatement update = (TUpdateSqlStatement) tCustomSqlStatement;
            if (update.getResultColumnList() != null) {
                for (int i = 0; i < update.getResultColumnList().size(); i++) {
                    TResultColumn field = update.getResultColumnList().getResultColumn(i);
                    if (field.getExpr() != null && field.getExpr().getRightOperand() != null) {
                        TExpression rightOperand = field.getExpr().getRightOperand();
                        if (rightOperand.getExpressionType() == EExpressionType.subquery_t) {
                            hasSelectWhere(rightOperand.getSubQuery());
                            if (!hasSelectWhere(rightOperand.getSubQuery())) {
                                return false;
                            }
                        }
                    }
                }
                return true;
            }
        }
        return true;
    }

    public static boolean hasSelectWhere(TSelectSqlStatement tSelectSqlStatement) {
        if (tSelectSqlStatement != null) {
            List<TSelectSqlStatement> tSelectSqlStatements = extractAllSelectFromSelectUnion(tSelectSqlStatement, 0);
            for (TSelectSqlStatement selectSqlStatement : tSelectSqlStatements) {
                if (selectSqlStatement.getWhereClause() == null) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    public static List<TSelectSqlStatement> extractAllSelectFromSelectUnion(TSelectSqlStatement selectStatement, int level) {
        if (Objects.isNull(selectStatement)) {
            return new ArrayList<>();
        } else {
            List<TSelectSqlStatement> list = new ArrayList<>();
            if (selectStatement.getLeftStmt() != null) {
                list.addAll(extractAllSelectFromSelectUnion(selectStatement.getLeftStmt(), level + 1));
            }
            if (selectStatement.getRightStmt() != null) {
                list.addAll(extractAllSelectFromSelectUnion(selectStatement.getRightStmt(), level + 1));
            }
            if (selectStatement.getLeftStmt() == null && selectStatement.getRightStmt() == null) {
                list.add(selectStatement);
            }
            return list;
        }
    }

    public static Set<String> getMongoDBAggregateFunction(CustomSqlStatement customSqlStatement) {
        FindSqlStatement findSqlStatement = (FindSqlStatement) customSqlStatement;
        if (SqlConstant.KEY_AGGREGATE.equalsIgnoreCase(findSqlStatement.getOperation())) {
            return new LinkedHashSet<>(Collections.singleton(SqlConstant.KEY_AGGREGATE));
        } else if (SqlConstant.KEY_COUNT.equalsIgnoreCase(findSqlStatement.getOperation())) {
            return new LinkedHashSet<>(Collections.singleton(SqlConstant.KEY_COUNT));
        } else if (SqlConstant.KEY_DISTINCT.equalsIgnoreCase(findSqlStatement.getOperation())) {
            return new LinkedHashSet<>(Collections.singleton(SqlConstant.KEY_DISTINCT));
        } else if (findSqlStatement.getCursorStatement() != null) {
            CursorStatement cursorStatement = findSqlStatement.getCursorStatement();
            if (cursorStatement.getCursorCondition() != null && StringUtils.isNotBlank(cursorStatement.getCursorCondition().getCursorOperation())) {
                return new LinkedHashSet<>(Collections.singleton(cursorStatement.getCursorCondition().getCursorOperation()));
            }
        }
        return new LinkedHashSet<>();
    }

    public static Set<String> getElasticSearchAggregateFunction(CustomSqlStatement customSqlStatement) {
        if (customSqlStatement instanceof GetSqlStatement) {
            GetSqlStatement getSqlStatement = (GetSqlStatement) customSqlStatement;
            if (SqlConstant.KEY_COUNT.equalsIgnoreCase(getSqlStatement.getOperation())) {
                return new LinkedHashSet<>(Collections.singleton(SqlConstant.KEY_COUNT));
            }
        } else if (customSqlStatement instanceof SelectSqlStatement) {
            try {
                SelectSqlStatement selectSqlStatement = (SelectSqlStatement) customSqlStatement;
                WhereCondition whereCondition = selectSqlStatement.getWhereCondition();
                if (whereCondition != null && StringUtils.isNotBlank(whereCondition.getStringValue())) {
                    Map<String, Object> map = gson.fromJson(whereCondition.getStringValue(), Map.class);
                    if (map.get("aggs") != null) {
                        return new LinkedHashSet<>(Collections.singleton(SqlConstant.KEY_AGGS));
                    }
                }
            } catch (Exception e) {
                log.error("es json parser error!", e);
            }
        }

        return new LinkedHashSet<>();
    }

    public static boolean useColonSplit(Integer dbType) {
        return Arrays.asList(DatabaseType.G_BASE_8S.getValue(), DatabaseType.INFORMIX.getValue()).contains(dbType);
    }

    public static String assembleColumnName(String columnName, Integer dbType) {
        if (Arrays.asList(DatabaseType.ORACLE.getValue(), DatabaseType.DM.getValue()).contains(dbType)) {
            columnName = String.format("\"%s\"", columnName);
        } else if (Arrays.asList(DatabaseType.MYSQL.getValue(), DatabaseType.TDMYSQL.getValue()).contains(dbType)) {
            columnName = String.format("`%s`", columnName);
        }
        return columnName;
    }

    public static String getObjectType(String sql, Integer dbType, CustomSqlStatement customSqlStatement, DCustomSqlStatement tCustomSqlStatement) {
        return getObjectType(getObjectType(sql, dbType, customSqlStatement), dbType, tCustomSqlStatement);
    }

    public static String getObjectType(String objectType, Integer dbType, DCustomSqlStatement tCustomSqlStatement) {
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            SqlParseObject sqlParserObject = parserObject(tCustomSqlStatement, dbType);
            if (sqlParserObject != null) {
                return sqlParserObject.getType();
            }
        }
        return objectType;
    }

    public static String getObjectOperation(String operation, Integer dbType, DCustomSqlStatement tCustomSqlStatement) {
        if (DatabaseType.SQL_SERVER.getValue().equals(dbType)) {
            SqlParseObject sqlParserObject = parserObject(tCustomSqlStatement, dbType);
            if (sqlParserObject != null) {
                return sqlParserObject.getOperation();
            }
        }
        return operation;
    }

    public static SqlParseObject parserObject(DCustomSqlStatement tCustomSqlStatement, Integer dbType) {
        try {
            if (tCustomSqlStatement instanceof TMssqlExecute) {
                String pattern = "(EXEC\\s+|EXECUTE\\s+)?(SP_RENAME)\\s+([0-9a-zA-Z_\"'\\[\\]`.:]+)\\s*(,)\\s*([0-9a-zA-Z_\"'\\[\\]`.:]+)";
                Pattern p = Pattern.compile(pattern, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());

                if (m.find()) {
                    SqlParseObject sqlParserObject = new SqlParseObject();
                    sqlParserObject.setType(SqlConstant.KEY_TABLE);
                    sqlParserObject.setName(m.group(3));
                    sqlParserObject.setOperation(SqlConstant.KEY_ALTER);
                    return sqlParserObject;
                }
            } else if (DatabaseType.DORIS.getValue().equals(dbType) && tCustomSqlStatement instanceof TDescribeStmt) {
                //doris : (desc|describe) table_name [all]
                String regex = "(?si)^\\s*(DESC|DESCRIBE)\\s+([0-9a-zA-Z_]+)(\\s+ALL)?";
                Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher m = p.matcher(tCustomSqlStatement.toString());
                if (m.find()) {
                    SqlParseObject sqlParserObject = new SqlParseObject();
                    sqlParserObject.setType(SqlConstant.KEY_TABLE);
                    sqlParserObject.setName(m.group(2));
                    sqlParserObject.setOperation(SqlConstant.KEY_DESC);
                    return sqlParserObject;
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    public static String getTransformationTableName(String tableName, Integer dbType) {
        if (StringUtils.isNotBlank(tableName)) {
            String replaceNewName = CommonUtil.replace(tableName);
            if (tableName.equals(replaceNewName)) {
                if (DatabaseType.getIdentCode(DatabaseType.nameNeedChangeToUpper()).contains(dbType)) {
                    tableName = tableName.toUpperCase(Locale.ROOT);
                } else if (DatabaseType.getIdentCode(DatabaseType.nameNeedChangeToLower()).contains(dbType)) {
                    tableName = tableName.toLowerCase(Locale.ROOT);
                }
            } else {
                tableName = replaceNewName;
            }
        }
        return tableName;
    }

    public static boolean isTransformationTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            String replaceNewName = CommonUtil.replace(tableName);
            return tableName.equals(replaceNewName);
        }
        return true;
    }

    public static boolean isWhiteTable(String tableName, Integer dbType) {
        return SqlConstant.KEY_TABLE_DUAL.equalsIgnoreCase(tableName) && DatabaseType.getIdentCode(DatabaseType.getDualWhiteList()).contains(dbType);
    }

    public static boolean isInstanceLevelOperation(String operation, String ddlSubdivideOperation) {
        return Stream.of(SqlConstant.KEY_GRANT, SqlConstant.KEY_REVOKE, SqlConstant.KEY_BEGIN_END).anyMatch(s -> s.equalsIgnoreCase(operation))
                ||
               OperationAuthConstant.instance_level_sub_operations.stream()
                        .anyMatch(s -> s.equalsIgnoreCase(ddlSubdivideOperation));
    }

    public static boolean containsUnderline(String operation) {
        //原生的含有下划线的动作。
        Set<String> rawUnderlines = Set.of(SqlConstant.KEY_BEGIN_END);
        if (rawUnderlines.stream().anyMatch(s -> s.equalsIgnoreCase(operation))) {
            return false;
        }
        return operation.contains("_");
    }

    //从 SqlActionParserServiceImpl 迁移过来的
    public static boolean anonymousBlockOrContains(@Nonnull DCustomSqlStatement statement) {
        //statement self is an anonymous block
        boolean instanceOfFlag = blockInstance(statement);

        //the statement contains anonymous block;
        boolean containsFlag = false;
        if (statement instanceof TMssqlIfElse) {
            TMssqlIfElse mssqlIfElse = (TMssqlIfElse) statement;
            //第一顺位
            if (blockInstance(mssqlIfElse.getStmt())) {
                containsFlag = true;
            }
            //所有顺位
            for (DCustomSqlStatement each : mssqlIfElse.getStatements()) {
                if (blockInstance(each)) {
                    containsFlag = true;
                    break;
                }
            }
        }

        return instanceOfFlag || containsFlag;
    }

    public static boolean blockInstance(DCustomSqlStatement statement) {
        if (statement == null) {
            return false;
        }
        return (Stream.of(TCommonBlock.class, TMssqlBlock.class).anyMatch(c -> c.isInstance(statement)) && SqlParserUtil.lastKeywordTokenIsEnd(statement))
                ||
                statement.sqlstatementtype == ESqlStatementType.sstpostgresqlDo;
    }

}
